# Scientists Portrait Collage Generator

This Python script creates a collage of famous scientists' portraits downloaded from Wikipedia.

## Features

- Downloads portrait images of 7 famous scientists from Wikipedia
- Downloads Roboto-Bold font from Google Fonts
- Resizes images to 300x300 pixels while maintaining aspect ratio
- Adds scientist names as labels below each portrait
- Creates a neat 3-column grid collage with proper margins
- Handles errors gracefully with fallback options
- Saves the final collage as `scientists_collage.jpg`

## Scientists Included

1. <PERSON><PERSON><PERSON>
2. <PERSON><PERSON><PERSON><PERSON>
3. <PERSON>
4. <PERSON>
5. <PERSON>
6. <PERSON>
7. <PERSON>

## Requirements

- Python 3.6+
- Pillow (PIL)
- requests

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Simply run the script:
```bash
python scientists_collage.py
```

The script will:
1. Download the Roboto-Bold font (if not already present)
2. Download each scientist's portrait from Wikipedia
3. Process and resize the images
4. Create labeled versions with scientist names
5. Arrange them in a collage grid
6. Save the result as `scientists_collage.jpg`

## Error Handling

- If font download fails, the script falls back to the default system font
- If individual image downloads fail, those scientists are skipped
- All operations include proper error logging and graceful degradation

## Output

The final collage will be saved as `scientists_collage.jpg` in the same directory as the script.

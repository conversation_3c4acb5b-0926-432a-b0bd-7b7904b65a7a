#!/usr/bin/env python3
"""
Scientists Portrait Collage Generator

This script downloads portrait images of famous scientists from Wikipedia,
downloads the Roboto-Bold font, and creates a neat collage with labels.
"""

import os
import requests
from PIL import Image, ImageDraw, ImageFont
import io
from typing import List, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Scientists data with their Wikipedia image URLs
SCIENTISTS = [
    {
        "name": "<PERSON><PERSON><PERSON>",
        "url": "https://upload.wikimedia.org/wikipedia/commons/c/c0/Ampere_<PERSON>_1825.jpg"
    },
    {
        "name": "<PERSON><PERSON><PERSON>",
        "url": "https://upload.wikimedia.org/wikipedia/commons/9/9f/<PERSON>_<PERSON>_<PERSON>.png"
    },
    {
        "name": "<PERSON>",
        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/3/39/<PERSON>.jpg/256px-<PERSON>_<PERSON>.jpg"
    },
    {
        "name": "<PERSON>",
        "url": "https://upload.wikimedia.org/wikipedia/commons/5/52/Alessandro_Volta.jpeg"
    },
    {
        "name": "Georg Simon Ohm",
        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2b/Georg_Simon_Ohm3.jpg/256px-Georg_Simon_Ohm3.jpg"
    },
    {
        "name": "Nikola Tesla",
        "url": "https://upload.wikimedia.org/wikipedia/commons/7/79/Tesla_circa_1890.jpeg"
    },
    {
        "name": "Michael Faraday",
        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/M_Faraday_Th_Phillips_oil_1841-1842.jpg/256px-M_Faraday_Th_Phillips_oil_1841-1842.jpg"
    }
]

# Configuration
IMAGE_SIZE = (300, 300)
FONT_SIZE = 24
FONT_FILENAME = "Roboto-Bold.ttf"
COLLAGE_FILENAME = "scientists_collage.jpg"
GRID_COLS = 3
MARGIN = 30
LABEL_HEIGHT = 60

# Headers for requests to comply with policies
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}


def download_file(url: str, filename: str) -> bool:
    """Download a file from URL and save it locally."""
    try:
        logger.info(f"Downloading {filename} from {url}")
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()

        with open(filename, 'wb') as f:
            f.write(response.content)

        logger.info(f"Successfully downloaded {filename}")
        return True
    except Exception as e:
        logger.error(f"Failed to download {filename}: {e}")
        return False


def download_roboto_font() -> Optional[str]:
    """Download Roboto-Bold font if not present locally."""
    if os.path.exists(FONT_FILENAME):
        logger.info(f"Font {FONT_FILENAME} already exists")
        return FONT_FILENAME

    # Try multiple sources for Roboto-Bold font
    font_urls = [
        "https://github.com/google/fonts/raw/master/apache/roboto/Roboto-Bold.ttf",
        "https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2",
        "https://github.com/googlefonts/roboto/raw/main/src/hinted/Roboto-Bold.ttf"
    ]

    for url in font_urls:
        if download_file(url, FONT_FILENAME):
            return FONT_FILENAME

    logger.warning("Failed to download Roboto-Bold font, will use default font")
    return None


def download_image(url: str) -> Optional[Image.Image]:
    """Download an image from URL and return PIL Image object."""
    try:
        logger.info(f"Downloading image from {url}")
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()

        image = Image.open(io.BytesIO(response.content))
        return image.convert('RGB')  # Ensure RGB mode
    except Exception as e:
        logger.error(f"Failed to download image from {url}: {e}")
        return None


def resize_image(image: Image.Image, size: Tuple[int, int]) -> Image.Image:
    """Resize image to specified size while maintaining aspect ratio."""
    # Calculate the aspect ratio
    original_width, original_height = image.size
    target_width, target_height = size
    
    # Calculate scaling factor to fit within target size
    scale_w = target_width / original_width
    scale_h = target_height / original_height
    scale = min(scale_w, scale_h)
    
    # Calculate new size
    new_width = int(original_width * scale)
    new_height = int(original_height * scale)
    
    # Resize image
    resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    # Create a new image with target size and paste resized image centered
    result = Image.new('RGB', size, (255, 255, 255))  # White background
    paste_x = (target_width - new_width) // 2
    paste_y = (target_height - new_height) // 2
    result.paste(resized, (paste_x, paste_y))
    
    return result


def create_labeled_image(image: Image.Image, name: str, font_path: Optional[str]) -> Image.Image:
    """Create an image with the scientist's name label below it."""
    # Create new image with space for label
    labeled_height = IMAGE_SIZE[1] + LABEL_HEIGHT
    labeled_image = Image.new('RGB', (IMAGE_SIZE[0], labeled_height), (255, 255, 255))
    
    # Paste the portrait image at the top
    labeled_image.paste(image, (0, 0))
    
    # Add text label
    draw = ImageDraw.Draw(labeled_image)
    
    # Try to use custom font, fallback to default
    try:
        if font_path and os.path.exists(font_path):
            font = ImageFont.truetype(font_path, FONT_SIZE)
        else:
            font = ImageFont.load_default()
    except Exception as e:
        logger.warning(f"Failed to load font: {e}, using default")
        font = ImageFont.load_default()
    
    # Calculate text position (centered)
    bbox = draw.textbbox((0, 0), name, font=font)
    text_width = bbox[2] - bbox[0]
    text_x = (IMAGE_SIZE[0] - text_width) // 2
    text_y = IMAGE_SIZE[1] + (LABEL_HEIGHT - FONT_SIZE) // 2
    
    # Draw text
    draw.text((text_x, text_y), name, fill=(0, 0, 0), font=font)
    
    return labeled_image


def create_collage(labeled_images: List[Image.Image]) -> Image.Image:
    """Create a collage from labeled images in a grid layout."""
    num_images = len(labeled_images)
    rows = (num_images + GRID_COLS - 1) // GRID_COLS  # Ceiling division
    
    # Calculate collage dimensions
    image_width, image_height = labeled_images[0].size
    collage_width = GRID_COLS * image_width + (GRID_COLS + 1) * MARGIN
    collage_height = rows * image_height + (rows + 1) * MARGIN
    
    # Create collage canvas
    collage = Image.new('RGB', (collage_width, collage_height), (240, 240, 240))
    
    # Place images in grid
    for i, img in enumerate(labeled_images):
        row = i // GRID_COLS
        col = i % GRID_COLS
        
        x = MARGIN + col * (image_width + MARGIN)
        y = MARGIN + row * (image_height + MARGIN)
        
        collage.paste(img, (x, y))
    
    return collage


def main():
    """Main function to create the scientists collage."""
    logger.info("Starting scientists collage creation")
    
    # Download font
    font_path = download_roboto_font()
    
    # Download and process images
    labeled_images = []
    
    for scientist in SCIENTISTS:
        name = scientist["name"]
        url = scientist["url"]
        
        logger.info(f"Processing {name}")
        
        # Download image
        image = download_image(url)
        if image is None:
            logger.error(f"Skipping {name} due to download failure")
            continue
        
        # Resize image
        resized_image = resize_image(image, IMAGE_SIZE)
        
        # Create labeled image
        labeled_image = create_labeled_image(resized_image, name, font_path)
        labeled_images.append(labeled_image)
    
    if not labeled_images:
        logger.error("No images were successfully processed")
        return
    
    logger.info(f"Successfully processed {len(labeled_images)} images")
    
    # Create collage
    logger.info("Creating collage")
    collage = create_collage(labeled_images)
    
    # Save collage
    logger.info(f"Saving collage as {COLLAGE_FILENAME}")
    collage.save(COLLAGE_FILENAME, 'JPEG', quality=95)
    
    logger.info(f"Collage saved successfully as {COLLAGE_FILENAME}")
    logger.info(f"Collage dimensions: {collage.size}")


if __name__ == "__main__":
    main()
